@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 40 33% 98%;
    --foreground: 30 15% 15%;
    --card: 0 0% 100%;
    --card-foreground: 30 15% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 30 15% 15%;
    --primary: 30 40% 35%;
    --primary-foreground: 40 33% 98%;
    --secondary: 40 25% 92%;
    --secondary-foreground: 30 15% 15%;
    --muted: 40 25% 92%;
    --muted-foreground: 30 15% 45%;
    --accent: 40 30% 88%;
    --accent-foreground: 30 15% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 40 33% 98%;
    --border: 35 20% 85%;
    --input: 35 20% 85%;
    --ring: 30 40% 35%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 30 25% 8%;
    --foreground: 40 25% 92%;
    --card: 30 20% 12%;
    --card-foreground: 40 25% 92%;
    --popover: 30 20% 12%;
    --popover-foreground: 40 25% 92%;
    --primary: 40 35% 65%;
    --primary-foreground: 30 25% 8%;
    --secondary: 30 15% 18%;
    --secondary-foreground: 40 25% 92%;
    --muted: 30 15% 18%;
    --muted-foreground: 40 15% 70%;
    --accent: 30 20% 22%;
    --accent-foreground: 40 25% 92%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 40 25% 92%;
    --border: 30 15% 20%;
    --input: 30 15% 20%;
    --ring: 40 35% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    background: linear-gradient(
      135deg,
      hsl(40, 25%, 88%) 0%,
      hsl(35, 22%, 85%) 25%,
      hsl(30, 20%, 82%) 50%,
      hsl(25, 18%, 79%) 75%,
      hsl(20, 15%, 76%) 100%
    );
    background-attachment: fixed;
    min-height: 100vh;
  }

  .dark body {
    background: linear-gradient(
      135deg,
      hsl(30, 25%, 8%) 0%,
      hsl(28, 22%, 10%) 25%,
      hsl(26, 20%, 12%) 50%,
      hsl(24, 18%, 14%) 75%,
      hsl(22, 15%, 16%) 100%
    );
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-julius-sans-one;
  }
  p,
  a,
  li,
  span,
  button {
    @apply font-archivo-narrow;
  }
}

/* Line clamp utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
